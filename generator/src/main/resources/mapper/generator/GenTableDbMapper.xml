<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.data.generator.mapper.GenTableDbMapper">

    <resultMap type="GenTable" id="GenTableResult">
        <id     property="tableId"       column="table_id"      />
        <result property="tableName"     column="table_name"    />
        <result property="tableComment"  column="table_comment" />
        <result property="subTableName"  column="sub_table_name" />
        <result property="subTableFkName" column="sub_table_fk_name" />
        <result property="className"     column="class_name"    />
        <result property="tplCategory"   column="tpl_category"  />
        <result property="packageName"   column="package_name"  />
        <result property="moduleName"    column="module_name"   />
        <result property="businessName"  column="business_name" />
        <result property="functionName"  column="function_name" />
        <result property="functionAuthor" column="function_author" />
        <result property="genType"       column="gen_type"      />
        <result property="genPath"       column="gen_path"      />
        <result property="options"       column="options"       />
        <result property="createBy"      column="create_by"     />
        <result property="createTime"    column="create_time"   />
        <result property="updateBy"      column="update_by"     />
        <result property="updateTime"    column="update_time"   />
        <result property="remark"        column="remark"        />
        <collection property="columns" javaType="java.util.List" resultMap="GenTableColumnResult" />
    </resultMap>

    <resultMap type="GenTableColumn" id="GenTableColumnResult">
        <id     property="columnId"       column="column_id"      />
        <result property="tableId"        column="table_id"       />
        <result property="columnName"     column="column_name"    />
        <result property="columnComment"  column="column_comment" />
        <result property="columnType"     column="column_type"    />
        <result property="javaType"       column="java_type"      />
        <result property="javaField"      column="java_field"     />
        <result property="isPk"           column="is_pk"          />
        <result property="isIncrement"    column="is_increment"   />
        <result property="isRequired"     column="is_required"    />
        <result property="isInsert"       column="is_insert"      />
        <result property="isEdit"         column="is_edit"        />
        <result property="isList"         column="is_list"        />
        <result property="isQuery"        column="is_query"       />
        <result property="queryType"      column="query_type"     />
        <result property="htmlType"       column="html_type"      />
        <result property="dictType"       column="dict_type"      />
        <result property="sort"           column="sort"           />
        <result property="createBy"       column="create_by"      />
        <result property="createTime"     column="create_time"    />
        <result property="updateBy"       column="update_by"      />
        <result property="updateTime"     column="update_time"    />
    </resultMap>

    <select id="selectPageDbTableList" parameterType="GenTable" resultMap="GenTableResult">
        <if test="@com.data.common.helper.DataBaseHelper@isMySql()">
            select table_name, table_comment, create_time, update_time from information_schema.tables
            where table_name NOT LIKE 'xxl_job_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
            AND table_name NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(table_name) like lower(concat('%', #{genTable.tableName}, '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(table_comment) like lower(concat('%', #{genTable.tableComment}, '%'))
            </if>
            order by create_time desc
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isOracle()">
            select lower(dt.table_name) as table_name, dtc.comments as table_comment, uo.created as create_time, uo.last_ddl_time as update_time
            from user_tables dt, user_tab_comments dtc, user_objects uo
            where dt.table_name = dtc.table_name
            and dt.table_name = uo.object_name
            and uo.object_type = 'TABLE'
            AND dt.table_name NOT LIKE 'XXL_JOB_%' AND dt.table_name NOT LIKE 'GEN_%'
            AND lower(dt.table_name) NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(dt.table_name) like lower(concat(concat('%', #{genTable.tableName}), '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(dtc.comments) like lower(concat(concat('%', #{genTable.tableComment}), '%'))
            </if>
            order by uo.created desc
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isPostgerSql()">
            SELECT table_name, table_comment, create_time, update_time
            FROM (
                SELECT c.relname AS table_name,
                       obj_description(c.oid) AS table_comment,
                       CURRENT_TIMESTAMP AS create_time,
                       CURRENT_TIMESTAMP AS update_time
                FROM pg_class c
                    LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
                    AND c.relname !~ '^pg_'
                    AND (n.nspname = 'public'::name
                    OR n.nspname = 'dgdm'::name OR n.nspname = 'job'::name)
                    AND n.nspname <![CDATA[ <> ]]> ''::name
            ) list_table
            where table_name NOT LIKE 'xxl_job_%' AND table_name NOT LIKE 'gen_%'
            AND table_name NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(table_name) like lower(concat('%', #{genTable.tableName}, '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(table_comment) like lower(concat('%', #{genTable.tableComment}, '%'))
            </if>
            order by create_time desc
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isSqlServer()">
            SELECT cast(D.NAME as nvarchar) as table_name,
                   cast(F.VALUE as nvarchar) as table_comment,
                   crdate as create_time,
                   refdate as update_time
            FROM SYSOBJECTS D
                INNER JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID
                    AND F.MINOR_ID = 0 AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
                    AND D.NAME NOT LIKE 'xxl_job_%' AND D.NAME NOT LIKE 'gen_%'
                    AND D.NAME NOT IN (select table_name from gen_table)
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(D.NAME) like lower('%'+ #{genTable.tableName} +'%')
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(cast(F.VALUE as nvarchar)) like lower('%'+ #{genTable.tableComment} +'%')
            </if>
            order by D.crdate desc
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isClickHouse()">
            SELECT name as table_name,
                   comment as table_comment,
                   now() as create_time,
                   now() as update_time
            FROM system.tables
            WHERE database = currentDatabase()
            AND name NOT LIKE 'xxl_job_%' AND name NOT LIKE 'gen_%'
            <if test="genTable.tableName != null and genTable.tableName != ''">
                AND lower(name) like lower(concat('%', #{genTable.tableName}, '%'))
            </if>
            <if test="genTable.tableComment != null and genTable.tableComment != ''">
                AND lower(comment) like lower(concat('%', #{genTable.tableComment}, '%'))
            </if>
            order by create_time desc
        </if>
    </select>

    <select id="selectDbTableListByNames" resultMap="GenTableResult">
        <if test="@com.data.common.helper.DataBaseHelper@isMySql()">
            select table_name, table_comment, create_time, update_time from information_schema.tables
            where table_name NOT LIKE 'xxl_job_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
            and table_name in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                 #{name}
            </foreach>
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isOracle()">
            select lower(dt.table_name) as table_name, dtc.comments as table_comment, uo.created as create_time, uo.last_ddl_time as update_time
            from user_tables dt, user_tab_comments dtc, user_objects uo
            where dt.table_name = dtc.table_name
            and dt.table_name = uo.object_name
            and uo.object_type = 'TABLE'
            AND dt.table_name NOT LIKE 'XXL_JOB_%' AND dt.table_name NOT LIKE 'GEN_%'
            and lower(dt.table_name) in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isPostgerSql()">
            SELECT table_name, table_comment, create_time, update_time
            FROM (
                SELECT c.relname AS table_name,
                       obj_description(c.oid) AS table_comment,
                       CURRENT_TIMESTAMP AS create_time,
                       CURRENT_TIMESTAMP AS update_time
                FROM pg_class c
                    LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
                    AND c.relname !~ '^pg_'
                    AND (n.nspname = 'public'::name
                    OR n.nspname = 'dgdm'::name OR n.nspname = 'job'::name)
                    AND n.nspname <![CDATA[ <> ]]> ''::name
            ) list_table
            where table_name NOT LIKE 'xxl_job_%' and table_name NOT LIKE 'gen_%'
            and table_name in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isSqlServer()">
            SELECT cast(D.NAME as nvarchar) as table_name,
                   cast(F.VALUE as nvarchar) as table_comment,
                   crdate as create_time,
                   refdate as update_time
            FROM SYSOBJECTS D
                INNER JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID
                    AND F.MINOR_ID = 0 AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
                    AND D.NAME NOT LIKE 'xxl_job_%' AND D.NAME NOT LIKE 'gen_%'
                    AND D.NAME in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isClickHouse()">
            SELECT name as table_name,
                   comment as table_comment,
                   now() as create_time,
                   now() as update_time
            FROM system.tables
            WHERE database = currentDatabase()
            AND name NOT LIKE 'xxl_job_%' AND name NOT LIKE 'gen_%'
            AND name in
            <foreach collection="array" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
    </select>

    <select id="selectTableByName" parameterType="String" resultMap="GenTableResult">
        <if test="@com.data.common.helper.DataBaseHelper@isMySql()">
            select table_name, table_comment, create_time, update_time from information_schema.tables
            where table_name NOT LIKE 'xxl_job_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
            and table_name = #{tableName}
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isOracle()">
            select lower(dt.table_name) as table_name, dtc.comments as table_comment, uo.created as create_time, uo.last_ddl_time as update_time
            from user_tables dt, user_tab_comments dtc, user_objects uo
            where dt.table_name = dtc.table_name
            and dt.table_name = uo.object_name
            and uo.object_type = 'TABLE'
            AND dt.table_name NOT LIKE 'XXL_JOB_%' AND dt.table_name NOT LIKE 'GEN_%'
            and lower(dt.table_name) = #{tableName}
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isPostgerSql()">
            SELECT table_name, table_comment, create_time, update_time
            FROM (
                SELECT c.relname AS table_name,
                       obj_description(c.oid) AS table_comment,
                       CURRENT_TIMESTAMP AS create_time,
                       CURRENT_TIMESTAMP AS update_time
                FROM pg_class c
                    LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
                    AND c.relname !~ '^pg_'
                    AND (n.nspname = 'public'::name
                    OR n.nspname = 'dgdm'::name OR n.nspname = 'job'::name)
                    AND n.nspname <![CDATA[ <> ]]> ''::name
            ) list_table
            where table_name NOT LIKE 'xxl_job_%' and table_name NOT LIKE 'gen_%'
            and table_name = #{tableName}
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isSqlServer()">
            SELECT cast(D.NAME as nvarchar) as table_name,
                   cast(F.VALUE as nvarchar) as table_comment,
                   crdate as create_time,
                   refdate as update_time
            FROM SYSOBJECTS D
                INNER JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID
                    AND F.MINOR_ID = 0 AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
                    AND D.NAME NOT LIKE 'xxl_job_%' AND D.NAME NOT LIKE 'gen_%'
                    AND D.NAME = #{tableName}
        </if>
        <if test="@com.data.common.helper.DataBaseHelper@isClickHouse()">
            SELECT name as table_name,
                   comment as table_comment,
                   now() as create_time,
                   now() as update_time
            FROM system.tables
            WHERE database = currentDatabase()
            AND name NOT LIKE 'xxl_job_%' AND name NOT LIKE 'gen_%'
            AND name = #{tableName}
        </if>
    </select>

    <select id="selectDbTableColumnsByName" parameterType="String" resultMap="GenTableColumnResult">
        <if test="@com.data.common.helper.DynamicDataBaseHelper@isMySql()">
            select column_name,
                   (case when (is_nullable = 'no' <![CDATA[ && ]]> column_key != 'PRI') then '1' else '0' end) as is_required,
                   (case when column_key = 'PRI' then '1' else '0' end) as is_pk,
                   ordinal_position as sort,
                   column_comment,
                   (case when extra = 'auto_increment' then '1' else '0' end) as is_increment,
                   column_type
            from information_schema.columns where table_schema = (select database()) and table_name = (#{tableName})
            order by ordinal_position
        </if>
        <if test="@com.data.common.helper.DynamicDataBaseHelper@isOracle()">
            select lower(temp.column_name) as column_name,
                   temp.column_comment,
                   (case when temp.constraint_type = 'P' then '1' else '0' end) as is_pk,
                   (case when temp.constraint_type = 'P' then '0' else '1' end) as is_required,
                   temp.column_id as sort,
                   '0' as is_increment,
                   lower(temp.data_type) as column_type
            from (
                select col.column_id, col.column_name, col.data_type, colc.comments as column_comment, uc.constraint_type, row_number() over (partition by col.column_name order by uc.constraint_type nulls last) row_flg
                from user_tab_columns col
                left join user_col_comments colc on colc.table_name = col.table_name and colc.column_name = col.column_name
                left join user_cons_columns ucc on ucc.table_name = col.table_name and ucc.column_name = col.column_name
                left join user_constraints uc on uc.constraint_name = ucc.constraint_name
                where col.table_name = upper(#{tableName})
            ) temp
            WHERE temp.row_flg = 1
            ORDER BY temp.column_id
        </if>
        <if test="@com.data.common.helper.DynamicDataBaseHelper@isPostgerSql()">
            SELECT column_name, is_required, is_pk, sort, column_comment, is_increment, column_type
            FROM (
                SELECT c.relname AS table_name,
                       a.attname AS column_name,
                       d.description AS column_comment,
                       CASE WHEN a.attnotnull AND con.conname IS NULL THEN 1 ELSE 0
                       END AS is_required,
                       CASE WHEN con.conname IS NOT NULL THEN 1 ELSE 0
                       END AS is_pk,
                       a.attnum AS sort,
                       CASE WHEN "position"(pg_get_expr(ad.adbin, ad.adrelid),
                                            ((c.relname)::text || ('_'::text || (a.attname)::text))) > 0 THEN 1 ELSE 0
                       END AS is_increment,
                       btrim(
                           CASE WHEN t.typelem <![CDATA[ <> ]]> (0)::oid AND t.typlen = '-1'::integer THEN 'ARRAY'::text ELSE
                               CASE WHEN t.typtype = 'd'::"char" THEN format_type(t.typbasetype, NULL::integer)
                                    ELSE format_type(a.atttypid, NULL::integer) END
                           END, '"'::text
                       ) AS column_type
                FROM pg_attribute a
                    JOIN pg_class c ON c.oid = a.attrelid
                    JOIN pg_type t ON t.oid = a.atttypid
                    LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
                    LEFT JOIN pg_description d ON d.objoid = c.oid AND d.objsubid = a.attnum
                    LEFT JOIN pg_attrdef ad ON ad.adrelid = a.attrelid AND ad.adnum = a.attnum
                    LEFT JOIN pg_constraint con ON con.conrelid = a.attrelid AND (a.attnum = ANY (con.conkey))
                WHERE (c.relkind = ANY (ARRAY ['r'::"char", 'p'::"char"]))
                    AND a.attnum > 0
                    AND (n.nspname = 'public'::name
                    OR n.nspname = 'dgdm'::name OR n.nspname = 'job'::name)
                ORDER BY c.relname, a.attnum
            ) temp
            WHERE table_name = (#{tableName})
                AND column_type <![CDATA[ <> ]]> '-'
        </if>
        <if test="@com.data.common.helper.DynamicDataBaseHelper@isSqlServer()">
            SELECT
                cast(A.NAME as nvarchar) as column_name,
                cast(B.NAME as nvarchar) + (case when B.NAME = 'numeric' then '(' + cast(A.prec as nvarchar) + ',' + cast(A.scale as nvarchar) + ')' else '' end) as column_type,
                cast(G.[VALUE] as nvarchar) as column_comment,
                (SELECT 1 FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE Z WHERE TABLE_NAME = D.NAME and A.NAME = Z.column_name  ) as is_pk,
                colorder as sort
            FROM SYSCOLUMNS A
                LEFT JOIN SYSTYPES B ON A.XTYPE = B.XUSERTYPE
                INNER JOIN SYSOBJECTS D ON A.ID = D.ID AND D.XTYPE = 'U' AND D.NAME != 'DTPROPERTIES'
                LEFT JOIN SYS.EXTENDED_PROPERTIES G ON A.ID = G.MAJOR_ID AND A.COLID = G.MINOR_ID
                LEFT JOIN SYS.EXTENDED_PROPERTIES F ON D.ID = F.MAJOR_ID AND F.MINOR_ID = 0
            WHERE D.NAME = #{tableName} order by A.colorder
        </if>
        <if test="@com.data.common.helper.DynamicDataBaseHelper@isClickHouse()">
            SELECT name as column_name,
                   comment as column_comment,
                   '0' as is_pk,
                   '0' as is_required,
                   position as sort,
                   '0' as is_increment,
                   type as column_type
            FROM system.columns
            WHERE database = currentDatabase()
            AND table = #{tableName}
            ORDER BY position
        </if>
    </select>

</mapper>
