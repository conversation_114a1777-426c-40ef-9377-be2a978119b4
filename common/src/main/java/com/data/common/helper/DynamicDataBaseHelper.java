package com.data.common.helper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.data.common.enums.DataBaseType;
import com.data.common.exception.ServiceException;
import com.data.common.utils.spring.SpringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

/**
 * 动态数据源数据库助手
 * 用于在MyBatis XML中根据当前动态数据源判断数据库类型
 *
 * <AUTHOR> Li
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DynamicDataBaseHelper {

    private static final DynamicRoutingDataSource DS = SpringUtils.getBean(DynamicRoutingDataSource.class);

    /**
     * 获取当前动态数据源的数据库类型
     */
    public static DataBaseType getCurrentDataBaseType() {
        String currentDataSourceKey = DynamicDataSourceContextHolder.peek();
        if (ObjectUtil.isNull(currentDataSourceKey)) {
            // 如果没有设置动态数据源，使用默认数据源
            return DataBaseHelper.getDataBaseType();
        }
        
        DataSource dataSource = DS.getDataSource(currentDataSourceKey);
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            String databaseProductName = metaData.getDatabaseProductName();
            DataBaseType dataBaseType = DataBaseType.find(databaseProductName);
            log.debug("当前数据源[{}]的数据库类型: {}", currentDataSourceKey, dataBaseType);
            return dataBaseType;
        } catch (SQLException e) {
            log.error("获取数据源[{}]的数据库类型失败: {}", currentDataSourceKey, e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 判断当前动态数据源是否为MySQL
     */
    public static boolean isMySql() {
        return DataBaseType.MY_SQL == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为Oracle
     */
    public static boolean isOracle() {
        return DataBaseType.ORACLE == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为PostgreSQL
     */
    public static boolean isPostgerSql() {
        return DataBaseType.POSTGRE_SQL == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为SQL Server
     */
    public static boolean isSqlServer() {
        return DataBaseType.SQL_SERVER == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为ClickHouse
     */
    public static boolean isClickHouse() {
        return DataBaseType.CLICK_HOUSE == getCurrentDataBaseType();
    }
}
