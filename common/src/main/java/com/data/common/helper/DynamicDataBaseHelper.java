package com.data.common.helper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.data.common.enums.DataBaseType;
import com.data.common.exception.ServiceException;
import com.data.common.utils.ServletUtils;
import com.data.common.utils.spring.SpringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

/**
 * 动态数据源数据库助手
 * 用于在MyBatis XML中根据当前动态数据源判断数据库类型
 *
 * <AUTHOR> Li
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DynamicDataBaseHelper {

    private static final DynamicRoutingDataSource DS = SpringUtils.getBean(DynamicRoutingDataSource.class);

    /**
     * 获取当前动态数据源的数据库类型
     */
    public static DataBaseType getCurrentDataBaseType() {
        String currentDataSourceKey = getCurrentDataSourceKey();
        if (ObjectUtil.isNull(currentDataSourceKey)) {
            // 如果没有设置动态数据源，使用默认数据源
            return DataBaseHelper.getDataBaseType();
        }

        DataSource dataSource = DS.getDataSource(currentDataSourceKey);
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            String databaseProductName = metaData.getDatabaseProductName();
            DataBaseType dataBaseType = DataBaseType.find(databaseProductName);
            log.debug("当前数据源[{}]的数据库类型: {}", currentDataSourceKey, dataBaseType);
            return dataBaseType;
        } catch (SQLException e) {
            log.error("获取数据源[{}]的数据库类型失败: {}", currentDataSourceKey, e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取当前数据源Key
     * 优先从动态数据源上下文获取，如果没有则从HTTP请求头获取
     */
    private static String getCurrentDataSourceKey() {
        // 首先尝试从动态数据源上下文获取
        String currentDataSourceKey = DynamicDataSourceContextHolder.peek();
        if (StrUtil.isNotBlank(currentDataSourceKey)) {
            return currentDataSourceKey;
        }

        // 如果上下文中没有，尝试从HTTP请求头获取（对应@DS("#header.datasource")）
        try {
            String headerDataSource = ServletUtils.getRequest().getHeader("datasource");
            if (StrUtil.isNotBlank(headerDataSource)) {
                log.debug("从HTTP请求头获取数据源: {}", headerDataSource);
                return headerDataSource;
            }
        } catch (Exception e) {
            log.debug("无法从HTTP请求头获取数据源: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 判断当前动态数据源是否为MySQL
     */
    public static boolean isMySql() {
        return DataBaseType.MY_SQL == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为Oracle
     */
    public static boolean isOracle() {
        return DataBaseType.ORACLE == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为PostgreSQL
     */
    public static boolean isPostgerSql() {
        return DataBaseType.POSTGRE_SQL == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为SQL Server
     */
    public static boolean isSqlServer() {
        return DataBaseType.SQL_SERVER == getCurrentDataBaseType();
    }

    /**
     * 判断当前动态数据源是否为ClickHouse
     */
    public static boolean isClickHouse() {
        return DataBaseType.CLICK_HOUSE == getCurrentDataBaseType();
    }
}
